using System;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for handling global hotkeys that work even when the application doesn't have focus
    /// </summary>
    public class GlobalHotkeyService : IDisposable
    {
        // Windows API constants
        private const int WM_HOTKEY = 0x0312;
        private const int MOD_NONE = 0x0000;
        private const int VK_F1 = 0x70;

        // Windows API imports
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, int fsModifiers, int vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetConsoleWindow();

        private readonly int _hotkeyId;
        private readonly Thread _messageLoopThread;
        private readonly ManualResetEventSlim _stopEvent;
        private HotkeyForm _form;
        private bool _disposed = false;

        /// <summary>
        /// Event fired when F1 hotkey is pressed
        /// </summary>
        public event EventHandler F1Pressed;

        private bool _hotkeyRegistered = false;

        public GlobalHotkeyService()
        {
            _hotkeyId = 1; // Unique ID for our hotkey
            _stopEvent = new ManualResetEventSlim(false);

            // Start message loop in a separate thread
            _messageLoopThread = new Thread(MessageLoopWorker)
            {
                IsBackground = true,
                Name = "GlobalHotkeyMessageLoop"
            };
            _messageLoopThread.SetApartmentState(ApartmentState.STA);
            _messageLoopThread.Start();
        }

        /// <summary>
        /// Register the F1 global hotkey
        /// </summary>
        public bool RegisterF1Hotkey()
        {
            // Wait longer for the form to be created and ensure the handle is created
            int attempts = 0;
            while (_form == null && attempts < 50) // Wait up to 5 seconds
            {
                Thread.Sleep(100);
                attempts++;
            }

            if (_form == null)
            {
                Console.WriteLine("⚠ Cannot register F1 hotkey - form not created yet");
                return false;
            }

            // Ensure the window handle is created
            if (!_form.IsHandleCreated)
            {
                try
                {
                    var handle = _form.Handle; // This forces handle creation
                    Console.WriteLine($"Form handle created: {handle}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error creating form handle: {ex.Message}");
                    return false;
                }
            }

            try
            {
                bool success = false;
                if (_form.InvokeRequired)
                {
                    _form.Invoke(new Func<bool>(() =>
                     {
                         Console.WriteLine($"Attempting to register F1 hotkey with window handle: {_form.Handle}");
                         success = RegisterHotKey(_form.Handle, _hotkeyId, MOD_NONE, VK_F1);
                         if (success)
                         {
                             _hotkeyRegistered = true;
                             Console.WriteLine("✓ Global F1 hotkey registered successfully");
                             Console.WriteLine("  Press F1 anywhere to activate voice commands");
                         }
                         else
                         {
                             var lastError = Marshal.GetLastWin32Error();
                             Console.WriteLine($"⚠ Failed to register F1 hotkey (Error code: {lastError})");

                             // Provide more specific error messages
                             switch (lastError)
                             {
                                 case 1409: // ERROR_HOTKEY_ALREADY_REGISTERED
                                     Console.WriteLine("  F1 is already registered by another application");
                                     Console.WriteLine("  Try closing other applications that might use F1 (like help systems)");
                                     break;
                                 case 1400: // ERROR_INVALID_WINDOW_HANDLE
                                     Console.WriteLine("  Invalid window handle - form may not be properly initialized");
                                     break;
                                 case 87: // ERROR_INVALID_PARAMETER
                                     Console.WriteLine("  Invalid parameters passed to RegisterHotKey");
                                     break;
                                 default:
                                     Console.WriteLine($"  Windows error code: {lastError}");
                                     break;
                             }
                         }
                         return success;
                     }));
                }

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering F1 hotkey: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unregister the F1 global hotkey
        /// </summary>
        public void UnregisterF1Hotkey()
        {
            try
            {
                if (_form != null && _hotkeyRegistered)
                {
                    bool success = UnregisterHotKey(_form.Handle, _hotkeyId);
                    if (success)
                    {
                        _hotkeyRegistered = false;
                        Console.WriteLine("F1 hotkey unregistered successfully");
                    }
                    else
                    {
                        Console.WriteLine("Failed to unregister F1 hotkey");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error unregistering F1 hotkey: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if the F1 hotkey is currently registered
        /// </summary>
        public bool IsHotkeyRegistered => _hotkeyRegistered;

        /// <summary>
        /// Message loop worker that runs in a separate thread to handle Windows messages
        /// </summary>
        private void MessageLoopWorker()
        {
            try
            {
                Console.WriteLine("Starting hotkey message loop thread...");

                // Create a message-only window for receiving hotkey messages
                Application.EnableVisualStyles();
                _form = new HotkeyForm(this);

                Console.WriteLine("Created hotkey form, starting message loop...");
                Console.WriteLine($"Form handle: {_form.Handle}"); // Force handle creation

                // Use proper Windows message loop instead of DoEvents
                Application.Run(_form);

                Console.WriteLine("Message loop stopped.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in hotkey message loop: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            finally
            {
                if (_form != null)
                {
                    try
                    {
                        if (_form.InvokeRequired)
                        {
                            _form.Invoke(new Action(() => _form.Close()));
                        }
                        else
                        {
                            _form.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error closing form: {ex.Message}");
                    }
                    _form = null;
                }
            }
        }

        /// <summary>
        /// Handle hotkey message
        /// </summary>
        internal void OnHotkeyPressed(int hotkeyId)
        {
            Console.WriteLine($"Hotkey message received with ID: {hotkeyId} (expected: {_hotkeyId})");
            if (hotkeyId == _hotkeyId)
            {
                Console.WriteLine("F1 hotkey matched - firing event");
                F1Pressed?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// Dispose resources and unregister hotkey
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                UnregisterF1Hotkey();

                // Stop the message loop by closing the form
                if (_form != null)
                {
                    try
                    {
                        if (_form.InvokeRequired)
                        {
                            _form.Invoke(new Action(() => _form.Close()));
                        }
                        else
                        {
                            _form.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error closing form during dispose: {ex.Message}");
                    }
                }

                _stopEvent.Set();

                if (_messageLoopThread != null && _messageLoopThread.IsAlive)
                {
                    _messageLoopThread.Join(2000); // Wait up to 2 seconds for thread to finish
                }

                _stopEvent?.Dispose();
                _disposed = true;
            }
        }

        /// <summary>
        /// Hidden form for receiving Windows messages
        /// </summary>
        private class HotkeyForm : Form
        {
            private readonly GlobalHotkeyService _service;

            public HotkeyForm(GlobalHotkeyService service)
            {
                _service = service;

                // Create a proper message-only window
                this.WindowState = FormWindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Visible = false;
                this.FormBorderStyle = FormBorderStyle.None;
                this.Size = new System.Drawing.Size(0, 0);
                this.Location = new System.Drawing.Point(-32000, -32000); // Move off-screen

                // Set window style to be a message-only window
                this.SetStyle(ControlStyles.UserPaint, false);
                this.SetStyle(ControlStyles.AllPaintingInWmPaint, false);
                this.SetStyle(ControlStyles.DoubleBuffer, false);
            }

            protected override CreateParams CreateParams
            {
                get
                {
                    CreateParams cp = base.CreateParams;
                    // Make it a message-only window
                    cp.ExStyle |= 0x08000000; // WS_EX_NOACTIVATE
                    cp.Parent = new IntPtr(-3); // HWND_MESSAGE
                    return cp;
                }
            }

            protected override void SetVisibleCore(bool value)
            {
                // Never make this form visible
                base.SetVisibleCore(false);
            }

            /// <summary>
            /// Override WndProc to handle hotkey messages
            /// </summary>
            protected override void WndProc(ref Message m)
            {
                if (m.Msg == WM_HOTKEY)
                {
                    int hotkeyId = m.WParam.ToInt32();
                    Console.WriteLine($"WndProc received WM_HOTKEY message with ID: {hotkeyId}");
                    _service.OnHotkeyPressed(hotkeyId);
                }
                else
                {
                    base.WndProc(ref m);
                }
            }
        }
    }
}
