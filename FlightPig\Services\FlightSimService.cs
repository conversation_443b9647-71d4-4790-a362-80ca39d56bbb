using System;
using System.Linq;
using CTrue.FsConnect;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Central service for managing Flight Simulator connection and providing access to MSFS functionality
    /// </summary>
    public class FlightSimService
    {
        private IFsConnect _fsConnect;
        private bool _isConnected;
        private int _aircraftInfoDefinitionId;
        private AircraftInfo _currentAircraftInfo;

        /// <summary>
        /// Event fired when connection status changes
        /// </summary>
        public event EventHandler<bool> ConnectionStatusChanged;

        /// <summary>
        /// Event fired when aircraft data is received
        /// </summary>
        public event EventHandler<AircraftInfo> AircraftDataReceived;

        /// <summary>
        /// Gets whether MSFS is currently connected
        /// </summary>
        public bool IsConnected => _isConnected && _fsConnect != null;

        /// <summary>
        /// Gets whether MSFS is connected and aircraft data is available
        /// </summary>
        public bool IsConnectedWithAircraft => IsConnected && !string.IsNullOrEmpty(_currentAircraftInfo.Title);

        /// <summary>
        /// Gets the current aircraft information (only valid when IsConnectedWithAircraft is true)
        /// </summary>
        public AircraftInfo CurrentAircraftInfo => _currentAircraftInfo;

        /// <summary>
        /// Gets the FsConnect instance if connected, null otherwise
        /// </summary>
        /// <returns>IFsConnect instance or null if not connected</returns>
        public IFsConnect GetFsConnect()
        {
            return IsConnected ? _fsConnect : null;
        }

        /// <summary>
        /// Attempts to establish connection to MSFS
        /// </summary>
        /// <returns>True if connection was successful</returns>
        public bool TryConnect()
        {
            try
            {
                // Clean up any existing connection
                Disconnect();

                _fsConnect = new FsConnect();
                _fsConnect.FsDataReceived += OnFsDataReceived;

                // Connect to MSFS
                _fsConnect.Connect("FlightPig", "localhost", 500, SimConnectProtocol.Ipv4);

                // Register aircraft info data definition
                _aircraftInfoDefinitionId = _fsConnect.RegisterDataDefinition<AircraftInfo>();

                _isConnected = true;
                ConnectionStatusChanged?.Invoke(this, true);
                return true;
            }
            catch
            {
                // Clean up failed connection
                Disconnect();
                return false;
            }
        }

        /// <summary>
        /// Disconnects from MSFS
        /// </summary>
        public void Disconnect()
        {
            if (_fsConnect != null)
            {
                try
                {
                    _fsConnect.FsDataReceived -= OnFsDataReceived;
                    _fsConnect.Disconnect();
                }
                catch { }
                _fsConnect = null;
            }

            if (_isConnected)
            {
                _isConnected = false;
                _currentAircraftInfo = default;
                ConnectionStatusChanged?.Invoke(this, false);
            }
        }

        /// <summary>
        /// Requests current aircraft data from MSFS
        /// </summary>
        public void RequestAircraftData()
        {
            var fsConnect = GetFsConnect();
            if (fsConnect != null)
            {
                try
                {
                    fsConnect.RequestData(1, _aircraftInfoDefinitionId);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error requesting aircraft data: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Executes an action with the FsConnect instance if available
        /// </summary>
        /// <param name="action">Action to execute with FsConnect</param>
        /// <returns>True if action was executed (connection available), false otherwise</returns>
        public bool WithFsConnect(Action<IFsConnect> action)
        {
            var fsConnect = GetFsConnect();
            if (fsConnect != null)
            {
                try
                {
                    action(fsConnect);
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error executing FsConnect action: {ex.Message}");
                }
            }
            return false;
        }

        /// <summary>
        /// Executes a function with the FsConnect instance if available
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="func">Function to execute with FsConnect</param>
        /// <param name="defaultValue">Default value to return if connection not available</param>
        /// <returns>Function result or default value</returns>
        public T WithFsConnect<T>(Func<IFsConnect, T> func, T defaultValue = default)
        {
            var fsConnect = GetFsConnect();
            if (fsConnect != null)
            {
                try
                {
                    return func(fsConnect);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error executing FsConnect function: {ex.Message}");
                }
            }
            return defaultValue;
        }

        private void OnFsDataReceived(object sender, FsDataReceivedEventArgs e)
        {
            if (e.Data != null && e.Data.Count > 0 && e.Data.First() is AircraftInfo aircraftInfo)
            {
                _currentAircraftInfo = aircraftInfo;
                AircraftDataReceived?.Invoke(this, aircraftInfo);
            }
        }
    }
}
