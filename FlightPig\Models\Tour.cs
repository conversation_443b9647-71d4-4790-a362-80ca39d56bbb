using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a guided tour with multiple points of interest
    /// </summary>
    public class Tour
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("region")]
        public string Region { get; set; }

        [JsonPropertyName("country")]
        public string Country { get; set; }

        [JsonPropertyName("difficulty")]
        public string Difficulty { get; set; }

        [JsonPropertyName("estimatedDurationMinutes")]
        public int EstimatedDurationMinutes { get; set; }

        [JsonPropertyName("recommendedAircraft")]
        public List<string> RecommendedAircraft { get; set; } = new List<string>();

        [JsonPropertyName("startingLocation")]
        public PointOfInterest StartingLocation { get; set; }

        [JsonPropertyName("pointsOfInterest")]
        public List<PointOfInterest> PointsOfInterest { get; set; } = new List<PointOfInterest>();

        [JsonPropertyName("tags")]
        public List<string> Tags { get; set; } = new List<string>();

        [JsonPropertyName("introduction")]
        public string Introduction { get; set; }

        [JsonPropertyName("conclusion")]
        public string Conclusion { get; set; }

        [JsonPropertyName("minimumAltitude")]
        public double? MinimumAltitude { get; set; }

        [JsonPropertyName("maximumAltitude")]
        public double? MaximumAltitude { get; set; }

        [JsonPropertyName("weatherRequirements")]
        public string WeatherRequirements { get; set; }

        [JsonPropertyName("currentPoiIndex")]
        public int CurrentPoiIndex { get; set; } = 0;

        [JsonPropertyName("isActive")]
        public bool IsActive { get; set; } = false;

        [JsonPropertyName("startTime")]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// Get the current point of interest
        /// </summary>
        public PointOfInterest CurrentPoi => 
            CurrentPoiIndex >= 0 && CurrentPoiIndex < PointsOfInterest.Count 
                ? PointsOfInterest[CurrentPoiIndex] 
                : null;

        /// <summary>
        /// Get the next point of interest
        /// </summary>
        public PointOfInterest NextPoi => 
            CurrentPoiIndex + 1 < PointsOfInterest.Count 
                ? PointsOfInterest[CurrentPoiIndex + 1] 
                : null;

        /// <summary>
        /// Check if the tour is completed
        /// </summary>
        public bool IsCompleted => CurrentPoiIndex >= PointsOfInterest.Count;

        /// <summary>
        /// Get the total distance of the tour in nautical miles
        /// </summary>
        public double GetTotalDistanceNm()
        {
            if (PointsOfInterest.Count < 2) return 0;

            double totalDistance = 0;
            for (int i = 0; i < PointsOfInterest.Count - 1; i++)
            {
                var current = PointsOfInterest[i];
                var next = PointsOfInterest[i + 1];
                totalDistance += CalculateDistanceNm(current.Latitude, current.Longitude, 
                                                   next.Latitude, next.Longitude);
            }
            return totalDistance;
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistanceNm(double lat1, double lon1, double lat2, double lon2)
        {
            const double earthRadiusNm = 3440.065; // Earth radius in nautical miles
            
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLonRad = (lon2 - lon1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return earthRadiusNm * c;
        }

        /// <summary>
        /// Advance to the next point of interest
        /// </summary>
        public bool AdvanceToNextPoi()
        {
            if (CurrentPoiIndex < PointsOfInterest.Count - 1)
            {
                CurrentPoiIndex++;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Convert tour to a mission with objectives
        /// </summary>
        public Mission ToMission()
        {
            var mission = new Mission
            {
                Title = Title,
                Description = Description,
                Region = Region,
                Difficulty = Difficulty,
                EstimatedDuration = TimeSpan.FromMinutes(EstimatedDurationMinutes),
                StartTime = DateTime.Now,
                Objectives = new List<Objective>()
            };

            // Add objectives for each POI
            for (int i = 0; i < PointsOfInterest.Count; i++)
            {
                var poi = PointsOfInterest[i];
                var objective = new Objective
                {
                    Title = $"Visit {poi.Name}",
                    Description = poi.Description,
                    Type = ObjectiveType.TourPointOfInterest,
                    Latitude = poi.Latitude,
                    Longitude = poi.Longitude,
                    Altitude = poi.Altitude,
                    Parameters = new Dictionary<string, object>
                    {
                        ["poiId"] = poi.Id,
                        ["tourGuideText"] = poi.TourGuideText,
                        ["nextPoiInstructions"] = poi.NextPoiInstructions,
                        ["isLandingRequired"] = poi.IsLandingRequired,
                        ["proximityThresholdNm"] = poi.ProximityThresholdNm
                    }
                };
                mission.Objectives.Add(objective);
            }

            return mission;
        }
    }
}
