using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a flight mission with objectives
    /// </summary>
    public class Mission
    {
        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("objectives")]
        public List<Objective> Objectives { get; set; }

        [JsonPropertyName("startTime")]
        public DateTime? StartTime { get; set; }

        [JsonPropertyName("estimatedDuration")]
        public TimeSpan? EstimatedDuration { get; set; }

        [JsonPropertyName("difficulty")]
        public string Difficulty { get; set; }

        [JsonPropertyName("region")]
        public string Region { get; set; }

        /// <summary>
        /// Calculate overall mission progress based on objective completion
        /// </summary>
        public double OverallProgress
        {
            get
            {
                if (Objectives.Count == 0) return 0.0;
                return Objectives.Average(o => o.Progress);
            }
        }

        /// <summary>
        /// Check if the mission is completed
        /// </summary>
        public bool IsCompleted
        {
            get
            {
                return Objectives.Count > 0 && Objectives.All(o => o.Completed);
            }
        }
    }
}
