@echo off
echo FlightPig - Whisper Model Downloader
echo ====================================
echo.

set MODEL_URL=https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin
set MODEL_PATH=ggml-tiny.bin

REM Check if model already exists
if exist "%MODEL_PATH%" (
    echo Whisper model already exists at: %MODEL_PATH%
    for %%A in ("%MODEL_PATH%") do (
        set /a SIZE=%%~zA/1024/1024
    )
    echo Model size: %SIZE% MB
    echo.
    echo If you want to re-download, delete the existing file first.
    pause
    exit /b 0
)

echo Downloading Whisper tiny model...
echo URL: %MODEL_URL%
echo Destination: %MODEL_PATH%
echo.
echo This will download approximately 39 MB and may take a few minutes...
echo.

REM Try PowerShell first (more reliable)
powershell -Command "& {try { Invoke-WebRequest -Uri '%MODEL_URL%' -OutFile '%MODEL_PATH%' -UseBasicParsing; Write-Host 'Download completed successfully!' -ForegroundColor Green } catch { Write-Host 'PowerShell download failed, trying curl...' -ForegroundColor Yellow; exit 1 }}"

if %ERRORLEVEL% EQU 0 goto :success

REM Fallback to curl if available
echo Trying curl...
curl -L -o "%MODEL_PATH%" "%MODEL_URL%"

if %ERRORLEVEL% EQU 0 goto :success

REM Fallback to manual instructions
echo.
echo Download failed. Please download manually:
echo.
echo 1. Open your web browser
echo 2. Go to: %MODEL_URL%
echo 3. Save the file as: %MODEL_PATH%
echo 4. Place it in the FlightPig application directory
echo.
pause
exit /b 1

:success
if exist "%MODEL_PATH%" (
    for %%A in ("%MODEL_PATH%") do (
        set /a SIZE=%%~zA/1024/1024
    )
    echo.
    echo Download completed successfully!
    echo Model saved to: %MODEL_PATH%
    echo Model size: %SIZE% MB
    echo.
    echo You can now run FlightPig with voice recognition enabled!
) else (
    echo Error: Downloaded file not found
    exit /b 1
)

echo.
pause
