Prompt 1

Please clone FsConnect from GitHub. Then convert it to .NET 8.0. Then create me a C# 8.0 console application called FlightPig, that references this upgraded FsConnect, to get airplane and location information from MSFS. Then create a system where we can query Ollama via it's WebAPI. On app startup, issue the pull call to ensure model "DeepSeek-R1 (8B)" is downloaded. 

For now let the console app show a menu 1, 2, 3 etc. Let 1 be "Start story mission". When 1 is pressed, send a request to Ollama to start a story mission. The request should include the current airplane and location information from MSFS. The response from Ollama should be a  structured result (maybe JSON), and there should be a "mission" object with a "title" and "description" property (at least). Display the title and description in the console. The mission should have one or more Objective objects, each with a title and description. Display each objective in the console. Objectives should have a type as well, and could be FlyOver, LandAt, LandAtAndWait, etc. 

Then FlightPig should monitor the data from FsConnect, and evaluate the progress of the objective completions. For now, just show the progress in the console.

Let's start with this please.

