using Newtonsoft.Json;

namespace FlightPig.Models
{
    /// <summary>
    /// Application settings configuration
    /// </summary>
    public class AppSettings
    {
        [JsonProperty("ollama")]
        public OllamaSettings Ollama { get; set; } = new OllamaSettings();

        [JsonProperty("whisper")]
        public WhisperSettings Whisper { get; set; } = new WhisperSettings();

        [JsonProperty("elevenlabs")]
        public ElevenLabsSettings ElevenLabs { get; set; } = new ElevenLabsSettings();

        [JsonProperty("voice")]
        public VoiceSettings Voice { get; set; } = new VoiceSettings();
    }

    public class OllamaSettings
    {
        [JsonProperty("baseUrl")]
        public string BaseUrl { get; set; } = "http://localhost:11434";

        [JsonProperty("modelName")]
        public string ModelName { get; set; } = "deepseek-r1:8b";

        [<PERSON>sonProperty("timeoutMinutes")]
        public int TimeoutMinutes { get; set; } = 30;
    }

    public class WhisperSettings
    {
        [JsonProperty("modelName")]
        public string ModelName { get; set; } = "ggml-tiny.bin";

        [JsonProperty("language")]
        public string Language { get; set; } = "en";

        [JsonProperty("recordingTimeoutSeconds")]
        public int RecordingTimeoutSeconds { get; set; } = 10;

        [JsonProperty("silenceThresholdMs")]
        public int SilenceThresholdMs { get; set; } = 2000;

        [JsonProperty("enabled")]
        public bool Enabled { get; set; } = true;
    }

    public class ElevenLabsSettings
    {
        [JsonProperty("apiKey")]
        public string ApiKey { get; set; } = "";

        [JsonProperty("baseUrl")]
        public string BaseUrl { get; set; } = "https://api.elevenlabs.io/v1";

        [JsonProperty("defaultVoiceId")]
        public string DefaultVoiceId { get; set; } = "21m00Tcm4TlvDq8ikWAM"; // Rachel voice

        [JsonProperty("pilotVoiceId")]
        public string PilotVoiceId { get; set; } = "29vD33N1CtxCmqQRPOHJ"; // Drew voice

        [JsonProperty("enabled")]
        public bool Enabled { get; set; } = true;

        [JsonProperty("stability")]
        public double Stability { get; set; } = 0.5;

        [JsonProperty("similarityBoost")]
        public double SimilarityBoost { get; set; } = 0.5;
    }

    public class VoiceSettings
    {
        [JsonProperty("pushToTalkKey")]
        public string PushToTalkKey { get; set; } = "Space";

        [JsonProperty("voiceActivation")]
        public bool VoiceActivation { get; set; } = true;

        [JsonProperty("continuousListening")]
        public bool ContinuousListening { get; set; } = false;

        [JsonProperty("confirmationSounds")]
        public bool ConfirmationSounds { get; set; } = true;

        [JsonProperty("volume")]
        public double Volume { get; set; } = 0.8;
    }
}
