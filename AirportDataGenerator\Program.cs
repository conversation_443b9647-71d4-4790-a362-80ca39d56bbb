﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace AirportDataGenerator
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== FlightPig Airport Data Generator ===");
            Console.WriteLine("Downloading OpenFlights airport database...");
            Console.WriteLine();

            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Add("User-Agent",
                    "FlightPig/1.0 (https://github.com/MariusMyburg/FlightPig)");

                var airports = await FetchOpenFlightsDataAsync(httpClient);

                Console.WriteLine($"Successfully processed {airports.Count} airports");

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var json = JsonSerializer.Serialize(airports, jsonOptions);
                var outputPath = "../FlightPig/airports.json";

                await File.WriteAllTextAsync(outputPath, json);

                Console.WriteLine($"✅ Generated {outputPath}");
                Console.WriteLine($"📁 File size: {new FileInfo(outputPath).Length / 1024:N0} KB");

                DisplayStatistics(airports);

                Console.WriteLine("🎉 Airport data generation completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Environment.Exit(1);
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task<List<Airport>> FetchOpenFlightsDataAsync(HttpClient httpClient)
        {
            // Use OurAirports comprehensive database instead (43,000+ airports)
            const string OURAIRPORTS_URL = "https://davidmegginson.github.io/ourairports-data/airports.csv";
            const string RUNWAYS_URL = "https://davidmegginson.github.io/ourairports-data/runways.csv";

            Console.WriteLine("Downloading OurAirports comprehensive database...");
            var airportsData = await httpClient.GetStringAsync(OURAIRPORTS_URL);

            Console.WriteLine("Downloading runway data...");
            var runwaysData = await httpClient.GetStringAsync(RUNWAYS_URL);
            // Parse runway data first
            var runwaysByAirport = ParseRunwayData(runwaysData);
            Console.WriteLine($"Parsed {runwaysByAirport.Values.Sum(r => r.Count)} runways");

            var airports = new List<Airport>();
            var lines = airportsData.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            // Skip header line
            for (int i = 1; i < lines.Length; i++)
            {
                try
                {
                    var fields = ParseCsvLine(lines[i]);
                    if (fields.Length < 18) continue;

                    // OurAirports CSV format:
                    // id,ident,type,name,latitude_deg,longitude_deg,elevation_ft,continent,iso_country,iso_region,municipality,scheduled_service,gps_code,iata_code,local_code,home_link,wikipedia_link,keywords

                    var airportType = CleanField(fields[2]);

                    // Include all airport types (not just "airport")
                    if (string.IsNullOrEmpty(airportType)) continue;

                    var ident = CleanField(fields[1]);
                    var name = CleanField(fields[3]);
                    var lat = ParseDouble(fields[4]);
                    var lon = ParseDouble(fields[5]);

                    // Skip airports without valid coordinates
                    if (Math.Abs(lat) < 0.001 && Math.Abs(lon) < 0.001) continue;

                    var airport = new Airport
                    {
                        IcaoCode = CleanField(fields[12]), // gps_code (ICAO)
                        IataCode = CleanField(fields[13]), // iata_code
                        Name = name,
                        Latitude = lat,
                        Longitude = lon,
                        ElevationFeet = ParseDouble(fields[6]),
                        Country = CleanField(fields[8]), // iso_country
                        Region = CleanField(fields[9]), // iso_region
                        City = CleanField(fields[10]), // municipality
                        Type = DetermineAirportTypeFromOurAirports(airportType),
                        IsControlled = DetermineIfControlledFromOurAirports(airportType, name),
                        Difficulty = DetermineDifficultyFromOurAirports(airportType, name),
                        SuitableAircraft = DetermineSuitableAircraftFromOurAirports(airportType, name),
                        Description = $"{name} in {CleanField(fields[10])}, {CleanField(fields[8])}"
                    };

                    // Use ICAO code if available, otherwise use ident
                    if (string.IsNullOrEmpty(airport.IcaoCode))
                        airport.IcaoCode = ident;

                    // Add runways if available, otherwise create default
                    if (runwaysByAirport.ContainsKey(ident))
                    {
                        airport.Runways.AddRange(runwaysByAirport[ident]);
                    }
                    else
                    {
                        airport.Runways.Add(CreateDefaultRunway(airport));
                    }

                    airports.Add(airport);
                }
                catch
                {
                    // Skip malformed lines
                }
            }

            return airports.OrderBy(a => a.Country).ThenBy(a => a.Name).ToList();
        }

        static string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var current = "";
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                if (c == '"') inQuotes = !inQuotes;
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(current);
                    current = "";
                }
                else current += c;
            }
            fields.Add(current);
            return fields.ToArray();
        }

        static string CleanField(string field)
        {
            if (string.IsNullOrEmpty(field) || field == "\\N") return "";
            return field.Trim('"').Trim();
        }

        static double ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "\\N") return 0;
            return double.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out double result) ? result : 0;
        }

        static Dictionary<string, List<Runway>> ParseRunwayData(string csvData)
        {
            var runwaysByAirport = new Dictionary<string, List<Runway>>();
            var lines = csvData.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            // Skip header line
            for (int i = 1; i < lines.Length; i++)
            {
                try
                {
                    var fields = ParseCsvLine(lines[i]);
                    if (fields.Length < 20) continue;

                    // OurAirports runway format:
                    // id,airport_ref,airport_ident,length_ft,width_ft,surface,lighted,closed,le_ident,le_latitude_deg,le_longitude_deg,le_elevation_ft,le_heading_degT,le_displaced_threshold_ft,he_ident,he_latitude_deg,he_longitude_deg,he_elevation_ft,he_heading_degT,he_displaced_threshold_ft

                    var airportIdent = CleanField(fields[2]);
                    var lengthFt = ParseDouble(fields[3]);
                    var widthFt = ParseDouble(fields[4]);
                    var surface = CleanField(fields[5]);
                    var lighted = CleanField(fields[6]) == "1";
                    var closed = CleanField(fields[7]) == "1";
                    var leIdent = CleanField(fields[8]);

                    if (closed || string.IsNullOrEmpty(leIdent)) continue;

                    var runway = new Runway
                    {
                        Designation = leIdent,
                        Heading = ParseDouble(fields[12]),
                        LengthFeet = lengthFt,
                        WidthFeet = widthFt,
                        Surface = surface,
                        HasLights = lighted,
                        HasILS = false, // Would need additional data
                        ThresholdLatitude = ParseDouble(fields[9]),
                        ThresholdLongitude = ParseDouble(fields[10]),
                        ThresholdElevation = ParseDouble(fields[11])
                    };

                    if (!runwaysByAirport.ContainsKey(airportIdent))
                        runwaysByAirport[airportIdent] = new List<Runway>();

                    runwaysByAirport[airportIdent].Add(runway);
                }
                catch
                {
                    // Skip malformed runway lines
                }
            }

            return runwaysByAirport;
        }

        static AirportType DetermineAirportTypeFromOurAirports(string type)
        {
            return type.ToLower() switch
            {
                "large_airport" => AirportType.International,
                "medium_airport" => AirportType.Regional,
                "small_airport" => AirportType.Municipal,
                "heliport" => AirportType.Heliport,
                "seaplane_base" => AirportType.Seaplane,
                "balloonport" => AirportType.Private,
                "closed" => AirportType.Private,
                _ => AirportType.Municipal
            };
        }

        static bool DetermineIfControlledFromOurAirports(string type, string name)
        {
            var typeLower = type.ToLower();
            var nameLower = name.ToLower();

            if (typeLower == "large_airport") return true;
            if (typeLower == "medium_airport") return true;
            if (nameLower.Contains("international")) return true;
            if (nameLower.Contains("regional")) return true;
            if (nameLower.Contains("municipal")) return true;

            return false; // Small airports, heliports, etc. usually uncontrolled
        }

        static LandingDifficulty DetermineDifficultyFromOurAirports(string type, string name)
        {
            var typeLower = type.ToLower();
            var nameLower = name.ToLower();

            if (nameLower.Contains("mountain") || nameLower.Contains("short") || nameLower.Contains("challenging")) return LandingDifficulty.Expert;
            if (typeLower == "large_airport") return LandingDifficulty.Medium;
            if (typeLower == "medium_airport") return LandingDifficulty.Medium;
            if (nameLower.Contains("international")) return LandingDifficulty.Hard;

            return LandingDifficulty.Easy;
        }

        static List<string> DetermineSuitableAircraftFromOurAirports(string type, string name)
        {
            var typeLower = type.ToLower();
            var nameLower = name.ToLower();

            if (typeLower == "heliport") return new List<string> { "Helicopter" };
            if (typeLower == "seaplane_base") return new List<string> { "Seaplane" };
            if (typeLower == "large_airport") return new List<string> { "Jet", "Turboprop", "General Aviation", "Helicopter" };
            if (typeLower == "medium_airport") return new List<string> { "Turboprop", "General Aviation", "Helicopter" };

            return new List<string> { "General Aviation", "Helicopter" };
        }

        static Runway CreateDefaultRunway(Airport airport)
        {
            return new Runway
            {
                Designation = "01",
                Heading = 10,
                LengthFeet = airport.Type == AirportType.International ? 10000 : 5000,
                WidthFeet = airport.Type == AirportType.International ? 150 : 100,
                Surface = "Asphalt",
                HasLights = airport.IsControlled,
                HasILS = airport.Type == AirportType.International
            };
        }

        static void DisplayStatistics(List<Airport> airports)
        {
            Console.WriteLine();
            Console.WriteLine("=== STATISTICS ===");

            var typeStats = airports.GroupBy(a => a.Type).ToDictionary(g => g.Key.ToString(), g => g.Count());
            var countryStats = airports.GroupBy(a => a.Country).ToDictionary(g => g.Key, g => g.Count());

            Console.WriteLine("By Type:");
            foreach (var kvp in typeStats.OrderByDescending(x => x.Value))
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");

            Console.WriteLine();
            Console.WriteLine("Top 10 Countries:");
            foreach (var kvp in countryStats.OrderByDescending(x => x.Value).Take(10))
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");

            Console.WriteLine();
            Console.WriteLine("Sample Airports:");
            foreach (var airport in airports.Where(a => !string.IsNullOrEmpty(a.IataCode)).Take(10))
                Console.WriteLine($"  {airport.IcaoCode} ({airport.IataCode}) - {airport.Name}, {airport.Country}");

            Console.WriteLine();
        }
    }
}
