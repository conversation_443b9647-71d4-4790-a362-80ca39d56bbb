using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for communicating with Ollama API
    /// </summary>
    public class OllamaService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _modelName;

        public OllamaService(HttpClient httpClient, string baseUrl = "http://localhost:11434", string modelName = "deepseek-r1:8b")
        {
            _httpClient = httpClient;
            _baseUrl = baseUrl;
            _modelName = modelName;

            // Set a longer timeout for model downloads (30 minutes)
            _httpClient.Timeout = TimeSpan.FromMinutes(30);
        }

        /// <summary>
        /// Check if the model is already available locally
        /// </summary>
        public async Task<bool> IsModelAvailableAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{_baseUrl}/api/tags");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var tagsResponse = JsonSerializer.Deserialize<JsonElement>(content);

                    if (tagsResponse.TryGetProperty("models", out var models))
                    {
                        foreach (var model in models.EnumerateArray())
                        {
                            if (model.TryGetProperty("name", out var name) &&
                                name.GetString().StartsWith(_modelName.Split(':')[0]))
                            {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Ensure the model is downloaded and available
        /// </summary>
        public async Task<bool> PullModelAsync()
        {
            try
            {
                Console.WriteLine($"Checking if model '{_modelName}' is already available...");

                // First check if model is already available
                if (await IsModelAvailableAsync())
                {
                    Console.WriteLine($"Model '{_modelName}' is already available locally.");
                    return true;
                }

                Console.WriteLine($"Model '{_modelName}' not found locally. Downloading...");
                Console.WriteLine("This may take several minutes for large models. Please wait...");

                var pullRequest = new
                {
                    name = _modelName
                };

                var json = JsonSerializer.Serialize(pullRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                Console.WriteLine("Sending pull request to Ollama...");
                var response = await _httpClient.PostAsync($"{_baseUrl}/api/pull", content);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Model '{_modelName}' download completed successfully.");
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Failed to pull model: {response.StatusCode}");
                    Console.WriteLine($"Error details: {errorContent}");
                    return false;
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                Console.WriteLine($"Model download timed out. The model '{_modelName}' may be very large.");
                Console.WriteLine("Please try running 'ollama pull deepseek-r1:8b' manually from command line.");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error pulling model: {ex.Message}");
                if (ex.Message.Contains("timeout") || ex.Message.Contains("Timeout"))
                {
                    Console.WriteLine("This appears to be a timeout issue. Try running 'ollama pull deepseek-r1:8b' manually.");
                }
                return false;
            }
        }

        /// <summary>
        /// Generate a guided tour based on current aircraft and location information
        /// </summary>
        public async Task<Tour> GenerateTourAsync(AircraftInfo aircraftInfo)
        {
            try
            {
                var prompt = CreateTourPrompt(aircraftInfo);

                var chatRequest = new
                {
                    model = _modelName,
                    messages = new[]
                    {
                        new { role = "system", content = "You are a knowledgeable tour guide and flight instructor for Microsoft Flight Simulator. Generate engaging guided tours with points of interest based on the current aircraft location. Always respond with valid JSON in the exact format requested. Include detailed tour guide narration for each POI." },
                        new { role = "user", content = prompt }
                    },
                    stream = false,
                    format = "json"
                };

                var json = JsonSerializer.Serialize(chatRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync("/api/chat", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var chatResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    if (chatResponse.TryGetProperty("message", out var message) &&
                        message.TryGetProperty("content", out var messageContent))
                    {
                        var tourJson = messageContent.GetString();
                        if (!string.IsNullOrEmpty(tourJson))
                        {
                            var tour = JsonSerializer.Deserialize<Tour>(tourJson, new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true
                            });

                            if (tour != null)
                            {
                                Console.WriteLine($"Generated tour: {tour.Title}");
                                return tour;
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Error from Ollama API: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating tour: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Generate a mission based on current aircraft and location information
        /// </summary>
        public async Task<Mission> GenerateMissionAsync(AircraftInfo aircraftInfo)
        {
            try
            {
                var prompt = CreateMissionPrompt(aircraftInfo);
                
                var chatRequest = new
                {
                    model = _modelName,
                    messages = new[]
                    {
                        new { role = "system", content = "You are a flight mission generator for Microsoft Flight Simulator. Generate realistic and engaging flight missions based on the current aircraft and location. Always respond with valid JSON in the exact format requested." },
                        new { role = "user", content = prompt }
                    },
                    stream = false,
                    format = "json"
                };

                var json = JsonSerializer.Serialize(chatRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/chat", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var chatResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                    
                    if (chatResponse.TryGetProperty("message", out var message) &&
                        message.TryGetProperty("content", out var messageContent))
                    {
                        var missionJson = messageContent.GetString();
                        if (!string.IsNullOrEmpty(missionJson))
                        {
                            return JsonSerializer.Deserialize<Mission>(missionJson);
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Failed to generate mission: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating mission: {ex.Message}");
            }

            return null;
        }

        private string CreateTourPrompt(AircraftInfo aircraftInfo)
        {
            var onGroundStatus = aircraftInfo.OnGround ? "on the ground" : "in flight";
            var aircraftType = DetermineAircraftType(aircraftInfo.Title);
            var tourParameters = GetTourParametersForAircraft(aircraftType);

            return $@"Generate a guided tour specifically designed for a {aircraftType.ToLower()} aircraft and current location:

Aircraft: {aircraftInfo.Title}
Current Location: Latitude {aircraftInfo.Latitude:F4}, Longitude {aircraftInfo.Longitude:F4}
Altitude: {aircraftInfo.Altitude:F0} feet
Heading: {aircraftInfo.Heading:F0} degrees
Airspeed: {aircraftInfo.AirspeedKnots:F0} knots
Status: {onGroundStatus}

Create an engaging guided tour with {tourParameters.PoiCount} points of interest (POIs) near the current location. The tour should:
1. Include real landmarks, cities, airports, natural features, or historical sites within {tourParameters.MaxDistanceNm} nautical miles
2. Be arranged in a logical sequence to minimize zig-zagging
3. Be specifically designed for {aircraftType.ToLower()} aircraft capabilities and flight characteristics
4. Use altitudes between {tourParameters.MinAltitude} and {tourParameters.MaxAltitude} feet
5. Include detailed tour guide narration for each POI with historical, geographical, or interesting facts
6. Provide clear navigation instructions between POIs appropriate for {aircraftType.ToLower()} flight patterns
7. {tourParameters.SpecialInstructions}
8. {GetPoiTypeRecommendations(aircraftType)}

Respond with a JSON object in this exact format:
{{
  ""id"": ""unique-tour-id"",
  ""title"": ""Tour Title"",
  ""description"": ""Brief tour description"",
  ""region"": ""Region/State"",
  ""country"": ""Country"",
  ""difficulty"": ""Easy/Medium/Hard"",
  ""estimatedDurationMinutes"": {tourParameters.EstimatedDuration},
  ""recommendedAircraft"": [""{aircraftType}""],
  ""introduction"": ""Welcome message for the tour tailored to {aircraftType.ToLower()} flying"",
  ""conclusion"": ""Closing message for the tour"",
  ""tags"": [""relevant tags"", ""{aircraftType.ToLower()}""],
  ""minimumAltitude"": {tourParameters.MinAltitude},
  ""maximumAltitude"": {tourParameters.MaxAltitude},
  ""pointsOfInterest"": [
    {{
      ""id"": ""poi-id"",
      ""name"": ""POI Name"",
      ""description"": ""Brief description"",
      ""latitude"": 0.0000,
      ""longitude"": 0.0000,
      ""altitude"": {tourParameters.TypicalPoiAltitude},
      ""category"": ""Landmark/Historical/Natural/Airport"",
      ""tourGuideText"": ""Detailed narration about this POI with interesting facts, history, or significance. This should be engaging and informative, as if spoken by a knowledgeable tour guide. Tailor the information to what would be visible and interesting from a {aircraftType.ToLower()}."",
      ""nextPoiInstructions"": ""Clear navigation instructions to reach the next POI, including heading, altitude, and what to look for. Use {aircraftType.ToLower()}-appropriate flight techniques and speeds."",
      ""tags"": [""relevant tags""],
      ""minimumAltitude"": {tourParameters.MinPoiAltitude},
      ""maximumAltitude"": {tourParameters.MaxPoiAltitude},
      ""proximityThresholdNm"": {tourParameters.ProximityThreshold:F1},
      ""isLandingRequired"": {tourParameters.LandingRequired.ToString().ToLower()}
    }}
  ]
}}

Make sure all coordinates are accurate for real locations and the tour guide text is informative and engaging.";
        }

        private string CreateMissionPrompt(AircraftInfo aircraftInfo)
        {
            var onGroundStatus = aircraftInfo.OnGround ? "on the ground" : "in flight";
            
            return $@"Generate a flight mission for the following aircraft and situation:

Aircraft: {aircraftInfo.Title}
Current Location: Latitude {aircraftInfo.Latitude:F4}, Longitude {aircraftInfo.Longitude:F4}
Altitude: {aircraftInfo.Altitude:F0} feet
Heading: {aircraftInfo.Heading:F0} degrees
Airspeed: {aircraftInfo.AirspeedKnots:F0} knots
Status: {onGroundStatus}

Create a realistic and engaging mission with 2-4 objectives. The mission should be appropriate for the aircraft type and current location.

Respond with JSON in this exact format:
{{
  ""title"": ""Mission Title"",
  ""description"": ""Brief mission description"",
  ""objectives"": [
    {{
      ""title"": ""Objective Title"",
      ""description"": ""Objective description"",
      ""type"": ""FlyOver"",
      ""latitude"": 40.7128,
      ""longitude"": -74.0060,
      ""altitude"": 3000,
      ""completed"": false,
      ""progress"": 0.0
    }}
  ]
}}

Use objective types: FlyOver, LandAt, LandAtAndWait, TakeOffFrom, NavigateTo, MaintainAltitude, MaintainSpeed, ContactATC, FollowRoute";
        }

        /// <summary>
        /// Determine aircraft type based on aircraft title
        /// </summary>
        private string DetermineAircraftType(string aircraftTitle)
        {
            if (string.IsNullOrEmpty(aircraftTitle))
                return "General Aviation";

            var title = aircraftTitle.ToLower();

            // Helicopters
            if (title.Contains("helicopter") || title.Contains("heli") ||
                title.Contains("bell") || title.Contains("robinson") ||
                title.Contains("airbus h") || title.Contains("ec135") ||
                title.Contains("uh-") || title.Contains("ah-"))
                return "Helicopter";

            // Jets
            if (title.Contains("jet") || title.Contains("boeing") ||
                title.Contains("airbus") || title.Contains("747") ||
                title.Contains("737") || title.Contains("320") ||
                title.Contains("citation") || title.Contains("learjet") ||
                title.Contains("gulfstream") || title.Contains("falcon") ||
                title.Contains("f-") || title.Contains("f/a-") ||
                title.Contains("fighter") || title.Contains("military"))
                return "Jet";

            // Turboprops
            if (title.Contains("turboprop") || title.Contains("king air") ||
                title.Contains("caravan") || title.Contains("twin otter") ||
                title.Contains("dash") || title.Contains("atr") ||
                title.Contains("saab") || title.Contains("pilatus"))
                return "Turboprop";

            // Gliders
            if (title.Contains("glider") || title.Contains("sailplane") ||
                title.Contains("discus") || title.Contains("ask"))
                return "Glider";

            // Seaplanes
            if (title.Contains("seaplane") || title.Contains("floatplane") ||
                title.Contains("icon a5") || title.Contains("beaver") ||
                title.Contains("otter") && title.Contains("float"))
                return "Seaplane";

            // Default to General Aviation for small aircraft
            return "General Aviation";
        }

        /// <summary>
        /// Get POI type recommendations based on aircraft type
        /// </summary>
        private string GetPoiTypeRecommendations(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "Prioritize urban landmarks, bridges, stadiums, harbors, and detailed architectural features that benefit from low-altitude viewing and hovering capabilities.",
                "Jet" => "Focus on major geographical features like mountain ranges, large cities, coastlines, major airports, and landmarks visible from high altitude.",
                "Turboprop" => "Include regional airports, medium-sized cities, lakes, rivers, and landmarks that are well-suited for medium-altitude viewing.",
                "Glider" => "Emphasize ridge lines, thermal sources, open fields for emergency landings, and soaring-friendly geographical features.",
                "Seaplane" => "Prioritize lakes, rivers, coastal areas, harbors, waterfront cities, and landmarks accessible by water landing.",
                _ => "Include a balanced mix of airports, cities, natural landmarks, and geographical features suitable for general aviation."
            };
        }

        /// <summary>
        /// Get tour parameters based on aircraft type
        /// </summary>
        private TourParameters GetTourParametersForAircraft(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => new TourParameters
                {
                    PoiCount = "4-6",
                    MaxDistanceNm = 25,
                    MinAltitude = 200,
                    MaxAltitude = 2000,
                    TypicalPoiAltitude = 800,
                    MinPoiAltitude = 200,
                    MaxPoiAltitude = 1500,
                    ProximityThreshold = 0.2,
                    EstimatedDuration = "45-75",
                    LandingRequired = false,
                    SpecialInstructions = "Focus on low-altitude sightseeing, detailed ground features, and hover capabilities. Include urban landmarks, bridges, and detailed architectural features that are best viewed from low altitude."
                },
                "Jet" => new TourParameters
                {
                    PoiCount = "3-4",
                    MaxDistanceNm = 100,
                    MinAltitude = 3000,
                    MaxAltitude = 15000,
                    TypicalPoiAltitude = 8000,
                    MinPoiAltitude = 3000,
                    MaxPoiAltitude = 12000,
                    ProximityThreshold = 1.0,
                    EstimatedDuration = "30-45",
                    LandingRequired = false,
                    SpecialInstructions = "Design for high-speed, high-altitude flight. Focus on large geographical features, mountain ranges, coastlines, and major cities visible from altitude. Use efficient routing for jet speeds."
                },
                "Turboprop" => new TourParameters
                {
                    PoiCount = "4-5",
                    MaxDistanceNm = 60,
                    MinAltitude = 1000,
                    MaxAltitude = 8000,
                    TypicalPoiAltitude = 3500,
                    MinPoiAltitude = 1000,
                    MaxPoiAltitude = 6000,
                    ProximityThreshold = 0.5,
                    EstimatedDuration = "40-60",
                    LandingRequired = false,
                    SpecialInstructions = "Balance between detail and efficiency. Include regional airports, medium-sized cities, and geographical features. Suitable for medium-altitude sightseeing."
                },
                "Glider" => new TourParameters
                {
                    PoiCount = "2-3",
                    MaxDistanceNm = 15,
                    MinAltitude = 1000,
                    MaxAltitude = 5000,
                    TypicalPoiAltitude = 2500,
                    MinPoiAltitude = 1000,
                    MaxPoiAltitude = 4000,
                    ProximityThreshold = 0.3,
                    EstimatedDuration = "60-90",
                    LandingRequired = false,
                    SpecialInstructions = "Focus on thermal-friendly routes and soaring opportunities. Include ridge lines, thermal sources, and landing options. Consider wind patterns and gliding performance."
                },
                "Seaplane" => new TourParameters
                {
                    PoiCount = "3-5",
                    MaxDistanceNm = 40,
                    MinAltitude = 500,
                    MaxAltitude = 3000,
                    TypicalPoiAltitude = 1200,
                    MinPoiAltitude = 500,
                    MaxPoiAltitude = 2500,
                    ProximityThreshold = 0.4,
                    EstimatedDuration = "45-70",
                    LandingRequired = true,
                    SpecialInstructions = "Emphasize water features, coastal areas, lakes, and rivers. Include water landing opportunities and waterfront landmarks. Focus on scenic water routes and amphibious capabilities."
                },
                _ => new TourParameters // General Aviation default
                {
                    PoiCount = "3-5",
                    MaxDistanceNm = 50,
                    MinAltitude = 1000,
                    MaxAltitude = 5000,
                    TypicalPoiAltitude = 2500,
                    MinPoiAltitude = 1000,
                    MaxPoiAltitude = 4000,
                    ProximityThreshold = 0.5,
                    EstimatedDuration = "40-60",
                    LandingRequired = false,
                    SpecialInstructions = "Create a balanced tour suitable for general aviation aircraft. Include a mix of landmarks, airports, and scenic areas at moderate altitudes."
                }
            };
        }
    }

    /// <summary>
    /// Tour parameters for different aircraft types
    /// </summary>
    public class TourParameters
    {
        public string PoiCount { get; set; }
        public int MaxDistanceNm { get; set; }
        public int MinAltitude { get; set; }
        public int MaxAltitude { get; set; }
        public int TypicalPoiAltitude { get; set; }
        public int MinPoiAltitude { get; set; }
        public int MaxPoiAltitude { get; set; }
        public double ProximityThreshold { get; set; }
        public string EstimatedDuration { get; set; }
        public bool LandingRequired { get; set; }
        public string SpecialInstructions { get; set; }
    }
}
