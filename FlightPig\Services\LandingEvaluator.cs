using System;
using System.Collections.Generic;
using System.Linq;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Evaluates landing performance and provides detailed scoring
    /// </summary>
    public class LandingEvaluator
    {
        private readonly List<AircraftInfo> _flightData;
        private LandingChallenge _currentChallenge;
        private bool _isEvaluating;
        private AircraftInfo? _lastAirborneData;
        private AircraftInfo? _touchdownData;
        private double _touchdownDistance;
        private DateTime _evaluationStartTime;

        public event EventHandler<LandingCompletedEventArgs> LandingCompleted;

        public LandingEvaluator()
        {
            _flightData = new List<AircraftInfo>();
        }

        /// <summary>
        /// Start evaluating a landing challenge
        /// </summary>
        public void StartEvaluation(LandingChallenge challenge)
        {
            _currentChallenge = challenge;
            _isEvaluating = true;
            _flightData.Clear();
            _lastAirborneData = null;
            _touchdownData = null;
            _touchdownDistance = 0;
            _evaluationStartTime = DateTime.Now;
            
            Console.WriteLine($"Started landing evaluation for {challenge.Airport.Name}");
        }

        /// <summary>
        /// Update flight data for evaluation
        /// </summary>
        public void UpdateFlightData(AircraftInfo aircraftInfo)
        {
            if (!_isEvaluating || _currentChallenge == null)
                return;

            _flightData.Add(aircraftInfo);

            // Check for touchdown
            if (_lastAirborneData.HasValue && !_lastAirborneData.Value.OnGround && aircraftInfo.OnGround)
            {
                _touchdownData = aircraftInfo;
                _touchdownDistance = CalculateDistanceFromThreshold(aircraftInfo);
                
                // Continue evaluation for rollout analysis
                Console.WriteLine($"Touchdown detected at {_touchdownDistance:F0} feet from threshold");
            }

            // Check if aircraft has stopped or evaluation timeout
            if (aircraftInfo.OnGround && aircraftInfo.AirspeedKnots < 5)
            {
                CompleteLandingEvaluation();
            }
            else if (DateTime.Now - _evaluationStartTime > TimeSpan.FromMinutes(10))
            {
                // Timeout - complete evaluation
                CompleteLandingEvaluation();
            }

            if (!aircraftInfo.OnGround)
            {
                _lastAirborneData = aircraftInfo;
            }
        }

        /// <summary>
        /// Complete the landing evaluation and calculate scores
        /// </summary>
        private void CompleteLandingEvaluation()
        {
            if (!_isEvaluating || _currentChallenge == null)
                return;

            _isEvaluating = false;

            var score = CalculateLandingScore();
            _currentChallenge.Score = score;
            _currentChallenge.EndTime = DateTime.Now;

            LandingCompleted?.Invoke(this, new LandingCompletedEventArgs(_currentChallenge));
            
            Console.WriteLine($"Landing evaluation completed. Overall score: {score.OverallScore:F1}% ({score.Grade})");
        }

        /// <summary>
        /// Calculate comprehensive landing score
        /// </summary>
        private LandingScore CalculateLandingScore()
        {
            var score = new LandingScore();
            var analysis = new LandingAnalysis();

            if (_flightData.Count == 0)
            {
                score.Grade = "F";
                score.Analysis = analysis;
                return score;
            }

            // Analyze flight data
            AnalyzeApproach(score, analysis);
            AnalyzeTouchdown(score, analysis);
            AnalyzeRollout(score, analysis);
            AnalyzeStability(score, analysis);

            // Calculate overall score
            score.CalculateOverallScore();

            // Generate achievements and improvements
            GenerateAchievementsAndImprovements(score, analysis);

            score.Analysis = analysis;
            return score;
        }

        /// <summary>
        /// Analyze approach phase
        /// </summary>
        private void AnalyzeApproach(LandingScore score, LandingAnalysis analysis)
        {
            if (_flightData.Count < 10)
            {
                score.ApproachScore = 0;
                return;
            }

            // Get approach data (last 30 seconds before touchdown)
            var approachData = _flightData.Where(d => !d.OnGround).TakeLast(30).ToList();
            if (!approachData.Any())
            {
                score.ApproachScore = 50; // Minimal score if no approach data
                return;
            }

            double approachScore = 100;

            // Check approach stability (consistent descent rate and speed)
            var speedVariation = CalculateSpeedVariation(approachData);
            var altitudeVariation = CalculateAltitudeVariation(approachData);

            if (speedVariation > 10) approachScore -= 20; // Speed too variable
            if (altitudeVariation > 200) approachScore -= 15; // Altitude too variable

            // Check final approach speed
            var finalApproachSpeed = approachData.LastOrDefault().AirspeedKnots;
            var targetSpeed = GetTargetApproachSpeed(_currentChallenge.AircraftType);
            var speedDeviation = Math.Abs(finalApproachSpeed - targetSpeed);

            if (speedDeviation > 20) approachScore -= 25;
            else if (speedDeviation > 10) approachScore -= 10;

            analysis.ApproachStability = speedVariation <= 10 && altitudeVariation <= 200;
            score.ApproachScore = Math.Max(0, approachScore);
        }

        /// <summary>
        /// Analyze touchdown phase
        /// </summary>
        private void AnalyzeTouchdown(LandingScore score, LandingAnalysis analysis)
        {
            if (!_touchdownData.HasValue)
            {
                score.TouchdownScore = 0;
                analysis.Crashed = true;
                return;
            }

            double touchdownScore = 100;

            // Touchdown distance from threshold
            analysis.TouchdownDistanceFeet = _touchdownDistance;
            var runway = _currentChallenge.TargetRunway;
            var targetTouchdownZone = runway.LengthFeet * 0.2; // First 20% of runway

            if (_touchdownDistance > targetTouchdownZone)
            {
                touchdownScore -= 30;
                analysis.LongLanding = true;
            }

            // Touchdown speed
            analysis.TouchdownSpeedKnots = _touchdownData.Value.AirspeedKnots;
            var targetTouchdownSpeed = GetTargetTouchdownSpeed(_currentChallenge.AircraftType);
            var speedDeviation = Math.Abs(_touchdownData.Value.AirspeedKnots - targetTouchdownSpeed);

            if (speedDeviation > 15) touchdownScore -= 20;
            else if (speedDeviation > 10) touchdownScore -= 10;

            // Vertical speed (simulated - would need actual data)
            analysis.VerticalSpeedFpm = -200; // Placeholder
            if (Math.Abs(analysis.VerticalSpeedFpm) > 500)
            {
                touchdownScore -= 25;
                analysis.HardLanding = true;
            }

            analysis.SmoothTouchdown = !analysis.HardLanding;
            analysis.ProperFlare = analysis.TouchdownSpeedKnots < targetTouchdownSpeed + 10;

            score.TouchdownScore = Math.Max(0, touchdownScore);
        }

        /// <summary>
        /// Analyze rollout phase
        /// </summary>
        private void AnalyzeRollout(LandingScore score, LandingAnalysis analysis)
        {
            var rolloutData = _flightData.Where(d => d.OnGround).ToList();
            if (!rolloutData.Any())
            {
                score.RolloutScore = 0;
                return;
            }

            double rolloutScore = 100;

            // Calculate rollout distance
            var firstGroundData = rolloutData.First();
            var lastGroundData = rolloutData.Last();
            analysis.RolloutDistanceFeet = CalculateDistance(firstGroundData, lastGroundData) * 6076; // Convert NM to feet

            // Check if aircraft stayed on runway centerline (simplified)
            analysis.CenterlineTracking = true; // Would need actual runway position data

            // Check rollout distance efficiency
            var runway = _currentChallenge.TargetRunway;
            if (analysis.RolloutDistanceFeet > runway.LengthFeet * 0.8)
            {
                rolloutScore -= 20; // Used too much runway
            }

            score.RolloutScore = Math.Max(0, rolloutScore);
        }

        /// <summary>
        /// Analyze overall stability
        /// </summary>
        private void AnalyzeStability(LandingScore score, LandingAnalysis analysis)
        {
            double stabilityScore = 100;

            // Check for excessive G-forces (simulated)
            analysis.MaxGForce = 1.5; // Placeholder
            if (analysis.MaxGForce > 2.0)
            {
                stabilityScore -= 30;
            }

            score.StabilityScore = Math.Max(0, stabilityScore);
        }

        /// <summary>
        /// Generate achievements and improvement suggestions
        /// </summary>
        private void GenerateAchievementsAndImprovements(LandingScore score, LandingAnalysis analysis)
        {
            // Achievements
            if (score.OverallScore >= 95) score.Achievements.Add("Perfect Landing");
            if (analysis.SmoothTouchdown) score.Achievements.Add("Smooth as Silk");
            if (analysis.CenterlineTracking) score.Achievements.Add("Centerline Master");
            if (!analysis.LongLanding) score.Achievements.Add("Precision Touchdown");

            // Improvements
            if (score.ApproachScore < 80) score.Improvements.Add("Work on approach stability and speed control");
            if (analysis.HardLanding) score.Improvements.Add("Practice flare technique for softer touchdowns");
            if (analysis.LongLanding) score.Improvements.Add("Aim for earlier touchdown in the touchdown zone");
            if (score.RolloutScore < 80) score.Improvements.Add("Improve braking and directional control during rollout");
        }

        // Helper methods
        private double CalculateDistanceFromThreshold(AircraftInfo aircraftInfo)
        {
            var runway = _currentChallenge.TargetRunway;
            if (runway.ThresholdLatitude.HasValue && runway.ThresholdLongitude.HasValue)
            {
                return CalculateDistance(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    runway.ThresholdLatitude.Value, runway.ThresholdLongitude.Value) * 6076; // Convert to feet
            }
            return 1000; // Default estimate
        }

        private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
        {
            const double earthRadiusNm = 3440.065;
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLonRad = (lon2 - lon1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return earthRadiusNm * c;
        }

        private double CalculateDistance(AircraftInfo a1, AircraftInfo a2)
        {
            return CalculateDistance(a1.Latitude, a1.Longitude, a2.Latitude, a2.Longitude);
        }

        private double CalculateSpeedVariation(List<AircraftInfo> data)
        {
            if (data.Count < 2) return 0;
            var speeds = data.Select(d => d.AirspeedKnots).ToList();
            var avg = speeds.Average();
            return speeds.Select(s => Math.Abs(s - avg)).Average();
        }

        private double CalculateAltitudeVariation(List<AircraftInfo> data)
        {
            if (data.Count < 2) return 0;
            var altitudes = data.Select(d => d.Altitude).ToList();
            var avg = altitudes.Average();
            return altitudes.Select(a => Math.Abs(a - avg)).Average();
        }

        private double GetTargetApproachSpeed(string aircraftType)
        {
            return aircraftType switch
            {
                "Jet" => 140,
                "Turboprop" => 100,
                "Helicopter" => 60,
                "Glider" => 50,
                _ => 80 // General Aviation
            };
        }

        private double GetTargetTouchdownSpeed(string aircraftType)
        {
            return aircraftType switch
            {
                "Jet" => 130,
                "Turboprop" => 85,
                "Helicopter" => 0, // Helicopters can land at zero speed
                "Glider" => 45,
                _ => 70 // General Aviation
            };
        }
    }

    /// <summary>
    /// Event args for landing completion
    /// </summary>
    public class LandingCompletedEventArgs : EventArgs
    {
        public LandingChallenge Challenge { get; }

        public LandingCompletedEventArgs(LandingChallenge challenge)
        {
            Challenge = challenge;
        }
    }
}
