using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace FlightPig.Services
{
    /// <summary>
    /// Service for caching ElevenLabs audio files to avoid repeated API calls
    /// </summary>
    public class AudioCacheService
    {
        private readonly string _cacheDirectory;
        private readonly string _indexFilePath;
        private readonly Dictionary<string, CacheEntry> _cacheIndex;
        private readonly object _lockObject = new object();

        public AudioCacheService()
        {
            _cacheDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "FlightPig", "AudioCache");
            _indexFilePath = Path.Combine(_cacheDirectory, "cache_index.json");
            _cacheIndex = new Dictionary<string, CacheEntry>();

            InitializeCache();
        }

        /// <summary>
        /// Cache entry metadata
        /// </summary>
        private class CacheEntry
        {
            public string Hash { get; set; }
            public string FilePath { get; set; }
            public DateTime CreatedAt { get; set; }
            public DateTime LastAccessedAt { get; set; }
            public long FileSizeBytes { get; set; }
            public string Text { get; set; }
            public string VoiceId { get; set; }
            public string ModelId { get; set; }
            public double Stability { get; set; }
            public double SimilarityBoost { get; set; }
        }

        /// <summary>
        /// Initialize the cache directory and load existing index
        /// </summary>
        private void InitializeCache()
        {
            try
            {
                // Create cache directory if it doesn't exist
                if (!Directory.Exists(_cacheDirectory))
                {
                    Directory.CreateDirectory(_cacheDirectory);
                    Console.WriteLine($"Created audio cache directory: {_cacheDirectory}");
                }

                // Load existing cache index
                if (File.Exists(_indexFilePath))
                {
                    var indexJson = File.ReadAllText(_indexFilePath);
                    var entries = JsonSerializer.Deserialize<Dictionary<string, CacheEntry>>(indexJson);
                    if (entries != null)
                    {
                        foreach (var kvp in entries)
                        {
                            // Verify the cached file still exists
                            if (File.Exists(kvp.Value.FilePath))
                            {
                                _cacheIndex[kvp.Key] = kvp.Value;
                            }
                        }
                        Console.WriteLine($"Loaded {_cacheIndex.Count} cached audio files");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing audio cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Generate a cache key based on text and voice settings
        /// </summary>
        private string GenerateCacheKey(string text, string voiceId, string modelId, double stability, double similarityBoost)
        {
            var input = $"{text}|{voiceId}|{modelId}|{stability:F2}|{similarityBoost:F2}";
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToHexString(hashBytes).ToLowerInvariant();
            }
        }

        /// <summary>
        /// Check if audio is cached and return the cached data
        /// </summary>
        public Task<byte[]> GetCachedAudioAsync(string text, string voiceId, string modelId, double stability, double similarityBoost)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(voiceId))
                return null;

            var cacheKey = GenerateCacheKey(text, voiceId, modelId, stability, similarityBoost);

            lock (_lockObject)
            {
                if (_cacheIndex.TryGetValue(cacheKey, out var entry))
                {
                    try
                    {
                        if (File.Exists(entry.FilePath))
                        {
                            // Update last accessed time
                            entry.LastAccessedAt = DateTime.UtcNow;
                            SaveCacheIndex();

                            var audioData = File.ReadAllBytes(entry.FilePath);
                            Console.WriteLine($"🎵 Using cached audio for: \"{text.Substring(0, Math.Min(50, text.Length))}...\"");
                            return Task.FromResult(audioData);
                        }
                        else
                        {
                            // File was deleted, remove from index
                            _cacheIndex.Remove(cacheKey);
                            SaveCacheIndex();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error reading cached audio: {ex.Message}");
                        _cacheIndex.Remove(cacheKey);
                        SaveCacheIndex();
                    }
                }
            }

            return Task.FromResult<byte[]>(null);
        }

        /// <summary>
        /// Cache audio data for future use
        /// </summary>
        public async Task CacheAudioAsync(string text, string voiceId, string modelId, double stability, double similarityBoost, byte[] audioData)
        {
            if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(voiceId) || audioData == null || audioData.Length == 0)
                return;

            try
            {
                var cacheKey = GenerateCacheKey(text, voiceId, modelId, stability, similarityBoost);
                var fileName = $"{cacheKey}.mp3";
                var filePath = Path.Combine(_cacheDirectory, fileName);

                // Write audio data to file
                await File.WriteAllBytesAsync(filePath, audioData);

                lock (_lockObject)
                {
                    // Add to cache index
                    _cacheIndex[cacheKey] = new CacheEntry
                    {
                        Hash = cacheKey,
                        FilePath = filePath,
                        CreatedAt = DateTime.UtcNow,
                        LastAccessedAt = DateTime.UtcNow,
                        FileSizeBytes = audioData.Length,
                        Text = text.Length > 200 ? text.Substring(0, 200) + "..." : text, // Store truncated text for reference
                        VoiceId = voiceId,
                        ModelId = modelId,
                        Stability = stability,
                        SimilarityBoost = similarityBoost
                    };

                    SaveCacheIndex();
                }

                Console.WriteLine($"💾 Cached audio ({audioData.Length / 1024:F1} KB) for: \"{text.Substring(0, Math.Min(50, text.Length))}...\"");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error caching audio: {ex.Message}");
            }
        }

        /// <summary>
        /// Save the cache index to disk
        /// </summary>
        private void SaveCacheIndex()
        {
            try
            {
                var indexJson = JsonSerializer.Serialize(_cacheIndex, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_indexFilePath, indexJson);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving cache index: {ex.Message}");
            }
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        public CacheStats GetCacheStats()
        {
            lock (_lockObject)
            {
                long totalSize = 0;
                int totalFiles = _cacheIndex.Count;

                foreach (var entry in _cacheIndex.Values)
                {
                    totalSize += entry.FileSizeBytes;
                }

                return new CacheStats
                {
                    TotalFiles = totalFiles,
                    TotalSizeBytes = totalSize,
                    TotalSizeMB = totalSize / (1024.0 * 1024.0),
                    CacheDirectory = _cacheDirectory
                };
            }
        }

        /// <summary>
        /// Clean up old cache entries (older than specified days)
        /// </summary>
        public Task CleanupOldEntriesAsync(int maxAgeDays = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-maxAgeDays);
            var entriesToRemove = new List<string>();

            lock (_lockObject)
            {
                foreach (var kvp in _cacheIndex)
                {
                    if (kvp.Value.LastAccessedAt < cutoffDate)
                    {
                        entriesToRemove.Add(kvp.Key);
                    }
                }
            }

            foreach (var key in entriesToRemove)
            {
                try
                {
                    var entry = _cacheIndex[key];
                    if (File.Exists(entry.FilePath))
                    {
                        File.Delete(entry.FilePath);
                    }
                    _cacheIndex.Remove(key);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error cleaning up cache entry: {ex.Message}");
                }
            }

            if (entriesToRemove.Count > 0)
            {
                SaveCacheIndex();
                Console.WriteLine($"Cleaned up {entriesToRemove.Count} old cache entries");
            }

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Cache statistics
    /// </summary>
    public class CacheStats
    {
        public int TotalFiles { get; set; }
        public long TotalSizeBytes { get; set; }
        public double TotalSizeMB { get; set; }
        public string CacheDirectory { get; set; }
    }
}
