using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Service to fetch comprehensive airport data from free online sources
    /// </summary>
    public class AirportDataFetcher
    {
        private readonly HttpClient _httpClient;
        private readonly string _cacheDirectory;

        // OpenFlights airport data URL
        private const string OPENFLIGHTS_AIRPORTS_URL = "https://raw.githubusercontent.com/jpatokal/openflights/master/data/airports.dat";
        
        // OurAirports data URLs (if accessible)
        private const string OURAIRPORTS_AIRPORTS_URL = "https://davidmegginson.github.io/ourairports-data/airports.csv";
        private const string OURAIRPORTS_RUNWAYS_URL = "https://davidmegginson.github.io/ourairports-data/runways.csv";

        public AirportDataFetcher(HttpClient httpClient, string cacheDirectory = "cache")
        {
            _httpClient = httpClient;
            _cacheDirectory = cacheDirectory;
            
            if (!Directory.Exists(_cacheDirectory))
                Directory.CreateDirectory(_cacheDirectory);
        }

        /// <summary>
        /// Fetch comprehensive airport data from multiple sources
        /// </summary>
        public async Task<List<Airport>> FetchAirportDataAsync()
        {
            Console.WriteLine("Fetching comprehensive airport data from free sources...");
            
            var airports = new List<Airport>();
            
            try
            {
                // Try OpenFlights first (most reliable)
                var openFlightsAirports = await FetchOpenFlightsDataAsync();
                airports.AddRange(openFlightsAirports);
                Console.WriteLine($"Fetched {openFlightsAirports.Count} airports from OpenFlights");
                
                // Try to enhance with OurAirports data if available
                try
                {
                    var ourAirportsData = await FetchOurAirportsDataAsync();
                    airports = MergeAirportData(airports, ourAirportsData);
                    Console.WriteLine($"Enhanced with OurAirports data");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Could not fetch OurAirports data: {ex.Message}");
                }
                
                // Filter and enhance the data
                airports = FilterAndEnhanceAirports(airports);
                
                Console.WriteLine($"Final airport count: {airports.Count}");
                return airports;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching airport data: {ex.Message}");
                return CreateFallbackAirports();
            }
        }

        /// <summary>
        /// Fetch airport data from OpenFlights
        /// </summary>
        private async Task<List<Airport>> FetchOpenFlightsDataAsync()
        {
            var cacheFile = Path.Combine(_cacheDirectory, "openflights_airports.dat");
            string data;

            // Try to use cached data first
            if (File.Exists(cacheFile) && File.GetLastWriteTime(cacheFile) > DateTime.Now.AddDays(-7))
            {
                data = await File.ReadAllTextAsync(cacheFile);
                Console.WriteLine("Using cached OpenFlights data");
            }
            else
            {
                // Fetch fresh data
                data = await _httpClient.GetStringAsync(OPENFLIGHTS_AIRPORTS_URL);
                await File.WriteAllTextAsync(cacheFile, data);
                Console.WriteLine("Downloaded fresh OpenFlights data");
            }

            return ParseOpenFlightsData(data);
        }

        /// <summary>
        /// Parse OpenFlights CSV data format
        /// </summary>
        private List<Airport> ParseOpenFlightsData(string csvData)
        {
            var airports = new List<Airport>();
            var lines = csvData.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                try
                {
                    var fields = ParseCsvLine(line);
                    if (fields.Length < 14) continue;

                    // OpenFlights format:
                    // 0:Airport ID, 1:Name, 2:City, 3:Country, 4:IATA, 5:ICAO, 
                    // 6:Latitude, 7:Longitude, 8:Altitude, 9:Timezone, 10:DST, 
                    // 11:Tz database timezone, 12:Type, 13:Source

                    if (fields[12] != "airport") continue; // Only airports, not train stations

                    var airport = new Airport
                    {
                        IcaoCode = CleanField(fields[5]),
                        IataCode = CleanField(fields[4]),
                        Name = CleanField(fields[1]),
                        Latitude = ParseDouble(fields[6]),
                        Longitude = ParseDouble(fields[7]),
                        ElevationFeet = ParseDouble(fields[8]),
                        Country = CleanField(fields[3]),
                        City = CleanField(fields[2]),
                        Type = DetermineAirportType(fields[1], fields[5]),
                        IsControlled = DetermineIfControlled(fields[5], fields[1]),
                        Difficulty = DetermineDifficulty(fields[1], fields[5]),
                        SuitableAircraft = DetermineSuitableAircraft(fields[1], fields[5]),
                        Description = $"{CleanField(fields[1])} in {CleanField(fields[2])}, {CleanField(fields[3])}"
                    };

                    // Only include airports with valid ICAO codes and coordinates
                    if (!string.IsNullOrEmpty(airport.IcaoCode) && 
                        airport.IcaoCode != "\\N" && 
                        Math.Abs(airport.Latitude) > 0.001 && 
                        Math.Abs(airport.Longitude) > 0.001)
                    {
                        // Add a basic runway for airports without detailed runway data
                        airport.Runways.Add(CreateDefaultRunway(airport));
                        airports.Add(airport);
                    }
                }
                catch (Exception ex)
                {
                    // Skip malformed lines
                    Console.WriteLine($"Skipping malformed airport line: {ex.Message}");
                }
            }

            return airports;
        }

        /// <summary>
        /// Attempt to fetch OurAirports data for enhanced runway information
        /// </summary>
        private async Task<Dictionary<string, List<Runway>>> FetchOurAirportsDataAsync()
        {
            var runwayData = new Dictionary<string, List<Runway>>();
            
            try
            {
                var runwaysData = await _httpClient.GetStringAsync(OURAIRPORTS_RUNWAYS_URL);
                var lines = runwaysData.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                
                // Skip header line
                for (int i = 1; i < lines.Length; i++)
                {
                    try
                    {
                        var fields = ParseCsvLine(lines[i]);
                        if (fields.Length < 10) continue;

                        // OurAirports runway format (simplified):
                        // airport_ref, airport_ident, length_ft, width_ft, surface, le_ident, he_ident, etc.
                        
                        var airportIdent = CleanField(fields[2]); // airport_ident
                        var runway = new Runway
                        {
                            Designation = CleanField(fields[8]), // le_ident (low end identifier)
                            LengthFeet = ParseDouble(fields[3]),
                            WidthFeet = ParseDouble(fields[4]),
                            Surface = CleanField(fields[5]),
                            HasLights = true, // Assume major runways have lights
                            HasILS = false // Would need more detailed data
                        };

                        if (!runwayData.ContainsKey(airportIdent))
                            runwayData[airportIdent] = new List<Runway>();
                        
                        runwayData[airportIdent].Add(runway);
                    }
                    catch
                    {
                        // Skip malformed runway lines
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Could not fetch runway data: {ex.Message}");
            }

            return runwayData;
        }

        /// <summary>
        /// Merge airport data from multiple sources
        /// </summary>
        private List<Airport> MergeAirportData(List<Airport> airports, Dictionary<string, List<Runway>> runwayData)
        {
            foreach (var airport in airports)
            {
                if (runwayData.ContainsKey(airport.IcaoCode))
                {
                    airport.Runways.Clear();
                    airport.Runways.AddRange(runwayData[airport.IcaoCode]);
                }
            }
            return airports;
        }

        /// <summary>
        /// Filter and enhance airport data
        /// </summary>
        private List<Airport> FilterAndEnhanceAirports(List<Airport> airports)
        {
            return airports.Where(a => 
                !string.IsNullOrEmpty(a.IcaoCode) &&
                !string.IsNullOrEmpty(a.Name) &&
                Math.Abs(a.Latitude) > 0.001 &&
                Math.Abs(a.Longitude) > 0.001
            ).OrderBy(a => a.Country).ThenBy(a => a.Name).ToList();
        }

        /// <summary>
        /// Create fallback airports if data fetching fails
        /// </summary>
        private List<Airport> CreateFallbackAirports()
        {
            Console.WriteLine("Creating fallback airport data...");
            
            return new List<Airport>
            {
                new Airport
                {
                    IcaoCode = "KSFO",
                    IataCode = "SFO",
                    Name = "San Francisco International Airport",
                    Latitude = 37.6213,
                    Longitude = -122.3790,
                    ElevationFeet = 13,
                    Type = AirportType.International,
                    Country = "United States",
                    City = "San Francisco",
                    IsControlled = true,
                    HasILS = true,
                    Difficulty = LandingDifficulty.Medium,
                    SuitableAircraft = new List<string> { "Jet", "Turboprop", "General Aviation" },
                    Description = "Major international airport with challenging crosswinds",
                    Runways = new List<Runway>
                    {
                        new Runway
                        {
                            Designation = "28L",
                            Heading = 284,
                            LengthFeet = 11870,
                            WidthFeet = 200,
                            Surface = "Asphalt",
                            HasLights = true,
                            HasILS = true,
                            ThresholdLatitude = 37.6213,
                            ThresholdLongitude = -122.3790,
                            ThresholdElevation = 13
                        }
                    }
                },
                // Add more fallback airports for different regions...
            };
        }

        // Helper methods
        private string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var current = "";
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(current);
                    current = "";
                }
                else
                {
                    current += c;
                }
            }
            fields.Add(current);
            return fields.ToArray();
        }

        private string CleanField(string field)
        {
            if (string.IsNullOrEmpty(field) || field == "\\N") return "";
            return field.Trim('"').Trim();
        }

        private double ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "\\N") return 0;
            if (double.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out double result))
                return result;
            return 0;
        }

        private AirportType DetermineAirportType(string name, string icaoCode)
        {
            var nameLower = name.ToLower();
            var icaoLower = icaoCode.ToLower();

            if (nameLower.Contains("international")) return AirportType.International;
            if (nameLower.Contains("regional")) return AirportType.Regional;
            if (nameLower.Contains("municipal") || nameLower.Contains("county")) return AirportType.Municipal;
            if (nameLower.Contains("private") || nameLower.Contains("ranch")) return AirportType.Private;
            if (nameLower.Contains("military") || nameLower.Contains("air force") || nameLower.Contains("naval")) return AirportType.Military;
            if (nameLower.Contains("heliport") || nameLower.Contains("helipad")) return AirportType.Heliport;
            if (nameLower.Contains("seaplane") || nameLower.Contains("water")) return AirportType.Seaplane;
            if (nameLower.Contains("glider")) return AirportType.Gliderport;

            // Determine by ICAO code patterns
            if (icaoLower.StartsWith("k") && icaoLower.Length == 4) return AirportType.Regional; // US airports
            if (icaoLower.StartsWith("eg") || icaoLower.StartsWith("lfp") || icaoLower.StartsWith("eddf")) return AirportType.International; // Major European

            return AirportType.Regional; // Default
        }

        private bool DetermineIfControlled(string icaoCode, string name)
        {
            var nameLower = name.ToLower();

            // International and major airports are usually controlled
            if (nameLower.Contains("international")) return true;
            if (nameLower.Contains("regional")) return true;
            if (nameLower.Contains("municipal")) return true;

            // Private and small airports usually aren't
            if (nameLower.Contains("private")) return false;
            if (nameLower.Contains("ranch")) return false;
            if (nameLower.Contains("strip")) return false;

            // Major airport codes are usually controlled
            if (!string.IsNullOrEmpty(icaoCode) && icaoCode.Length == 4)
            {
                var icaoLower = icaoCode.ToLower();
                if (icaoLower.StartsWith("k") || icaoLower.StartsWith("eg") ||
                    icaoLower.StartsWith("lf") || icaoLower.StartsWith("ed")) return true;
            }

            return true; // Default to controlled for safety
        }

        private LandingDifficulty DetermineDifficulty(string name, string icaoCode)
        {
            var nameLower = name.ToLower();

            // Expert difficulty
            if (nameLower.Contains("mountain") || nameLower.Contains("high altitude")) return LandingDifficulty.Expert;
            if (nameLower.Contains("short") || nameLower.Contains("challenging")) return LandingDifficulty.Expert;

            // Hard difficulty
            if (nameLower.Contains("international") && (nameLower.Contains("busy") || nameLower.Contains("major"))) return LandingDifficulty.Hard;
            if (nameLower.Contains("crosswind") || nameLower.Contains("weather")) return LandingDifficulty.Hard;

            // Medium difficulty
            if (nameLower.Contains("international") || nameLower.Contains("regional")) return LandingDifficulty.Medium;

            // Easy difficulty
            if (nameLower.Contains("municipal") || nameLower.Contains("county")) return LandingDifficulty.Easy;
            if (nameLower.Contains("training") || nameLower.Contains("practice")) return LandingDifficulty.Easy;

            return LandingDifficulty.Medium; // Default
        }

        private List<string> DetermineSuitableAircraft(string name, string icaoCode)
        {
            var nameLower = name.ToLower();
            var suitable = new List<string>();

            // Heliports
            if (nameLower.Contains("heliport") || nameLower.Contains("helipad"))
            {
                suitable.Add("Helicopter");
                return suitable;
            }

            // Seaplane bases
            if (nameLower.Contains("seaplane") || nameLower.Contains("water"))
            {
                suitable.Add("Seaplane");
                return suitable;
            }

            // Gliderports
            if (nameLower.Contains("glider") || nameLower.Contains("soaring"))
            {
                suitable.Add("Glider");
                return suitable;
            }

            // International airports - all aircraft
            if (nameLower.Contains("international"))
            {
                suitable.AddRange(new[] { "Jet", "Turboprop", "General Aviation", "Helicopter" });
                return suitable;
            }

            // Regional airports - most aircraft except large jets
            if (nameLower.Contains("regional"))
            {
                suitable.AddRange(new[] { "Turboprop", "General Aviation", "Helicopter" });
                return suitable;
            }

            // Small airports - GA and helicopters
            if (nameLower.Contains("municipal") || nameLower.Contains("county") || nameLower.Contains("private"))
            {
                suitable.AddRange(new[] { "General Aviation", "Helicopter" });
                return suitable;
            }

            // Default - most aircraft types
            suitable.AddRange(new[] { "Turboprop", "General Aviation", "Helicopter" });
            return suitable;
        }

        private Runway CreateDefaultRunway(Airport airport)
        {
            // Create a reasonable default runway based on airport type
            var runway = new Runway
            {
                Designation = "01",
                Heading = 10, // Default heading
                Surface = "Asphalt",
                HasLights = airport.IsControlled,
                HasILS = airport.Type == AirportType.International
            };

            // Set runway dimensions based on airport type
            switch (airport.Type)
            {
                case AirportType.International:
                    runway.LengthFeet = 10000;
                    runway.WidthFeet = 150;
                    break;
                case AirportType.Regional:
                    runway.LengthFeet = 6000;
                    runway.WidthFeet = 100;
                    break;
                case AirportType.Municipal:
                    runway.LengthFeet = 4000;
                    runway.WidthFeet = 75;
                    break;
                case AirportType.Private:
                    runway.LengthFeet = 2500;
                    runway.WidthFeet = 50;
                    runway.Surface = "Grass";
                    break;
                case AirportType.Heliport:
                    runway.LengthFeet = 100;
                    runway.WidthFeet = 100;
                    runway.Surface = "Concrete";
                    runway.Designation = "H1";
                    break;
                default:
                    runway.LengthFeet = 5000;
                    runway.WidthFeet = 100;
                    break;
            }

            return runway;
        }
    }
}
